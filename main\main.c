
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_bt.h"

#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_bt_main.h"
#include "esp_bt_device.h"

#include "esp_gatt_common_api.h"
#include "ble_gap.h"
#include "ble_gatt.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"

#define GATTS_TABLE_TAG "GATTS_TABLE_DEMO"
#define BASE_PATH "/data"

// 初始化BLE
void ble_init()
{
    esp_err_t ret;

    // 此函数将BSS、数据和控制器的其他部分释放到堆中。总大小约为70KB
    // 如果您只打算使用BLE，调用esp_bt_controller_mem_release（esp_bt_MODE_CLASSIC_bt）可以释放BSS和经典蓝牙控制器消耗的数据。
    ret = esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT);
    ESP_ERROR_CHECK(ret);

    // 使用官方默认的参数去初始化蓝牙底层的硬件资源，固定写法，不用深究
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "%s enable controller failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 控制器初始化完以后，还要显式"启用"，否则不会工作, 固定写法, 不用深究
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "%s enable controller failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 初始化蓝牙协议栈
    // 控制器负责底层收发，协议栈负责 BLE 逻辑处理，比如 GATT 等  固定写法, 不用深究
    ret = esp_bluedroid_init();
    if (ret)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "%s init bluetooth failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 使能蓝牙协议栈
    // 初始化只是分配资源，启动才是真正开始运行  固定写法, 不用深究
    ret = esp_bluedroid_enable();
    if (ret)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "%s enable bluetooth failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 初始化 GAP 层
    ret = ble_gap_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "GAP init failed, error code = 0x%x", ret);
        return;
    }

    // 初始化 GATT 层
    ret = ble_gatt_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(GATTS_TABLE_TAG, "GATT init failed, error code = 0x%x", ret);
        return;
    }
}

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ret = nvs_flash_erase();
        ESP_ERROR_CHECK(ret);
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 挂载文件系统
    ret = mount_storage(BASE_PATH);
    ESP_ERROR_CHECK(ret);



 
    // 使用BLE与主从通讯
    ble_init();

    ESP_LOGW(GATTS_TABLE_TAG, "111");
}
