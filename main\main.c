
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_bt.h"
#include "driver/gpio.h"

#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_bt_main.h"
#include "esp_bt_device.h"

#include "esp_gatt_common_api.h"
#include "ble_gap.h"
#include "ble_gatt.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"
#include "local_ota.h"

/* Base path for file server */
#define BASE_PATH "/data"

#define TAG "[SLAVE]"

/* Use project configuration menu (idf.py menuconfig) to choose the GPIO to blink,
   or you can edit the following line and set a number here.
*/
#ifndef CONFIG_BLINK_GPIO
#define CONFIG_BLINK_GPIO 8  // Default for ESP32-C3-DevKit-1
#endif
#define BLINK_GPIO CONFIG_BLINK_GPIO

static uint8_t s_led_state = 0;

static void blink_led(void)
{
    /* Set the GPIO level according to the state (LOW or HIGH)*/
    gpio_set_level(BLINK_GPIO, s_led_state);
}

static void configure_led(void)
{
    ESP_LOGI(TAG, "Example configured to blink GPIO LED!");
    gpio_reset_pin(BLINK_GPIO);
    /* Set the GPIO as a push/pull output */
    gpio_set_direction(BLINK_GPIO, GPIO_MODE_OUTPUT);
}

// LED闪烁任务
void led_blink_task(void *pvParameters)
{
    /* Configure the LED */
    configure_led();

    while (1) {
        ESP_LOGI(TAG, "Turning the LED %s!", s_led_state == true ? "ON" : "OFF");
        blink_led();
        /* Toggle the LED state */
        s_led_state = !s_led_state;
        vTaskDelay(pdMS_TO_TICKS(1000));  // 1秒闪烁一次
    }
}

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ret = nvs_flash_erase();
        ESP_ERROR_CHECK(ret);
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 使用BLE与主从通讯
    // 此函数将BSS、数据和控制器的其他部分释放到堆中。总大小约为70KB
    // 如果您只打算使用BLE，调用esp_bt_controller_mem_release（esp_bt_MODE_CLASSIC_bt）可以释放BSS和经典蓝牙控制器消耗的数据。
    ret = esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT);
    ESP_ERROR_CHECK(ret);

    // 使用官方默认的参数去初始化蓝牙底层的硬件资源，固定写法，不用深究
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret)
    {
        ESP_LOGE(TAG, "%s enable controller failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 控制器初始化完以后，还要显式"启用"，否则不会工作, 固定写法, 不用深究
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret)
    {
        ESP_LOGE(TAG, "%s enable controller failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 初始化蓝牙协议栈
    // 控制器负责底层收发，协议栈负责 BLE 逻辑处理，比如 GATT 等  固定写法, 不用深究
    ret = esp_bluedroid_init();
    if (ret)
    {
        ESP_LOGE(TAG, "%s init bluetooth failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 使能蓝牙协议栈
    // 初始化只是分配资源，启动才是真正开始运行  固定写法, 不用深究
    ret = esp_bluedroid_enable();
    if (ret)
    {
        ESP_LOGE(TAG, "%s enable bluetooth failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 初始化 GAP 层
    ret = ble_gap_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "GAP init failed, error code = 0x%x", ret);
        return;
    }

    // 初始化 GATT 层
    ret = ble_gatt_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "GATT init failed, error code = 0x%x", ret);
        return;
    }

    // 挂载文件系统
    ret = mount_storage(BASE_PATH);
    ESP_ERROR_CHECK(ret);

    // 初始化本地OTA控制 (GPIO15控制WiFi AP和文件服务器)
    ret = local_ota_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize local OTA: %s", esp_err_to_name(ret));
        return;
    } 

    // 创建LED闪烁任务
    xTaskCreate(led_blink_task, "led_blink", 2048, NULL, 1, NULL);

    ESP_LOGI(TAG, "System initialization completed");
}
