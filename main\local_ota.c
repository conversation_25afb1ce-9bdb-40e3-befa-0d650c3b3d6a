/*
 * Local OTA Control Implementation
 * GPIO15 controlled WiFi AP and File Server
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_timer.h"

#include "local_ota.h"
#include "wifi_ap.h"
#include "file_server.h"

static const char *TAG = "local_ota";

/* Task and timer handles */
static TaskHandle_t gpio_monitor_task_handle = NULL;
static esp_timer_handle_t debounce_timer = NULL;

/* Current state tracking */
static local_ota_state_t current_state = LOCAL_OTA_STATE_DISABLED;
static bool services_running = false;
static bool initialization_complete = false;

/* Base path for file server */
#define BASE_PATH "/data"

/* Debounce time in microseconds (50ms) */
#define DEBOUNCE_TIME_US 50000

/**
 * @brief Debounce timer callback
 */
static void debounce_timer_callback(void* arg)
{
    local_ota_state_t new_state = (local_ota_state_t)gpio_get_level(LOCAL_OTA_GPIO_PIN);

    if (new_state != current_state) {
        ESP_LOGI(TAG, "GPIO%d state changed: %s -> %s", LOCAL_OTA_GPIO_PIN,
                 current_state == LOCAL_OTA_STATE_ENABLED ? "HIGH" : "LOW",
                 new_state == LOCAL_OTA_STATE_ENABLED ? "HIGH" : "LOW");

        current_state = new_state;
        
        if (current_state == LOCAL_OTA_STATE_ENABLED) {
            /* GPIO15 is high - start services */
            esp_err_t ret = local_ota_start_services();
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "Failed to start OTA services: %s", esp_err_to_name(ret));
            }
        } else {
            /* GPIO15 is low - stop services */
            esp_err_t ret = local_ota_stop_services();
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "Failed to stop OTA services: %s", esp_err_to_name(ret));
            }
        }
    }
}

/**
 * @brief GPIO interrupt handler
 */
static void IRAM_ATTR gpio_isr_handler(void* arg)
{
    /* Start/restart debounce timer */
    esp_timer_start_once(debounce_timer, DEBOUNCE_TIME_US);
}

/**
 * @brief GPIO monitoring task
 */
static void gpio_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "GPIO monitor task started");

    /* Initial state check */
    current_state = (local_ota_state_t)gpio_get_level(LOCAL_OTA_GPIO_PIN);
    ESP_LOGI(TAG, "Initial GPIO%d state: %s", LOCAL_OTA_GPIO_PIN,
             current_state == LOCAL_OTA_STATE_ENABLED ? "HIGH" : "LOW");

    /* Start services if GPIO is initially high */
    if (current_state == LOCAL_OTA_STATE_ENABLED) {
        esp_err_t ret = local_ota_start_services();
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to start initial OTA services: %s", esp_err_to_name(ret));
        }
    }

    /* Task loop - periodically print GPIO state and keep task alive */
    while (1) {
        int gpio_level = gpio_get_level(LOCAL_OTA_GPIO_PIN);
        ESP_LOGI(TAG, "GPIO%d current level: %s, Services: %s",
                 LOCAL_OTA_GPIO_PIN,
                 gpio_level ? "HIGH" : "LOW",
                 services_running ? "RUNNING" : "STOPPED");

        vTaskDelay(pdMS_TO_TICKS(3000)); // Print every 5 seconds
    }
}

esp_err_t local_ota_init(void)
{
    esp_err_t ret;
    
    ESP_LOGI(TAG, "Initializing local OTA control on GPIO%d", LOCAL_OTA_GPIO_PIN);
    
    /* Configure GPIO */
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_ANYEDGE,        /* Interrupt on both edges */
        .mode = GPIO_MODE_INPUT,               /* Input mode */
        .pin_bit_mask = (1ULL << LOCAL_OTA_GPIO_PIN),
        .pull_down_en = GPIO_PULLDOWN_ENABLE,  /* Enable pull-down */
        .pull_up_en = GPIO_PULLUP_DISABLE      /* Disable pull-up */
    };
    
    ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure GPIO: %s", esp_err_to_name(ret));
        return ret;
    }
    
    /* Install GPIO ISR service */
    ret = gpio_install_isr_service(0);
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "Failed to install GPIO ISR service: %s", esp_err_to_name(ret));
        return ret;
    }
    
    /* Create debounce timer */
    esp_timer_create_args_t timer_args = {
        .callback = debounce_timer_callback,
        .arg = NULL,
        .name = "ota_debounce"
    };
    
    ret = esp_timer_create(&timer_args, &debounce_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create debounce timer: %s", esp_err_to_name(ret));
        return ret;
    }
    
    /* Add ISR handler for GPIO */
    ret = gpio_isr_handler_add(LOCAL_OTA_GPIO_PIN, gpio_isr_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to add GPIO ISR handler: %s", esp_err_to_name(ret));
        esp_timer_delete(debounce_timer);
        return ret;
    }
    
    /* Create GPIO monitoring task */
    BaseType_t task_ret = xTaskCreate(
        gpio_monitor_task,
        "gpio_monitor",
        4096,
        NULL,
        5,
        &gpio_monitor_task_handle
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create GPIO monitor task");
        gpio_isr_handler_remove(LOCAL_OTA_GPIO_PIN);
        esp_timer_delete(debounce_timer);
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Local OTA control initialized successfully");
    return ESP_OK;
}

esp_err_t local_ota_deinit(void)
{
    ESP_LOGI(TAG, "Deinitializing local OTA control");
    
    /* Stop services if running */
    if (services_running) {
        local_ota_stop_services();
    }
    
    /* Remove GPIO ISR handler */
    gpio_isr_handler_remove(LOCAL_OTA_GPIO_PIN);
    
    /* Delete debounce timer */
    if (debounce_timer) {
        esp_timer_delete(debounce_timer);
        debounce_timer = NULL;
    }
    
    /* Delete monitoring task */
    if (gpio_monitor_task_handle) {
        vTaskDelete(gpio_monitor_task_handle);
        gpio_monitor_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "Local OTA control deinitialized");
    return ESP_OK;
}

local_ota_state_t local_ota_get_state(void)
{
    return current_state;
}

esp_err_t local_ota_start_services(void)
{
    if (services_running) {
        ESP_LOGW(TAG, "OTA services already running");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Starting WiFi AP and file server...");
    
    /* Start WiFi AP */
    esp_err_t ret = wifi_init_ap();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi AP: %s", esp_err_to_name(ret));
        return ret;
    }
    
    /* Start file server */
    ret = start_file_server(BASE_PATH);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start file server: %s", esp_err_to_name(ret));
        /* Try to stop WiFi AP on failure */
        wifi_deinit_ap();
        return ret;
    }
    
    services_running = true;
    ESP_LOGI(TAG, "OTA services started successfully");
    return ESP_OK;
}

esp_err_t local_ota_stop_services(void)
{
    if (!services_running) {
        ESP_LOGW(TAG, "OTA services not running");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Stopping WiFi AP and file server...");
    
    /* Stop file server */
    esp_err_t ret1 = stop_file_server();
    if (ret1 != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop file server: %s", esp_err_to_name(ret1));
    }
    
    /* Stop WiFi AP */
    esp_err_t ret2 = wifi_deinit_ap();
    if (ret2 != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop WiFi AP: %s", esp_err_to_name(ret2));
    }
    
    services_running = false;
    ESP_LOGI(TAG, "OTA services stopped");
    
    /* Return error if either operation failed */
    return (ret1 == ESP_OK && ret2 == ESP_OK) ? ESP_OK : ESP_FAIL;
}
